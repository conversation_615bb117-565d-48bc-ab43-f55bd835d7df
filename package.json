{"name": "one-web-blog", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@mdx-js/loader": "^3.1.0", "@mdx-js/react": "^3.1.0", "@next/mdx": "^15.4.6", "@shikijs/transformers": "^3.9.2", "@types/mdx": "^2.0.13", "clsx": "^2.1.1", "next": "15.4.6", "react": "19.1.0", "react-dom": "19.1.0", "rehype-pretty-code": "^0.14.1", "shiki": "^3.9.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@shikijs/rehype": "^3.9.2", "@tailwindcss/postcss": "^4", "@tailwindcss/typography": "^0.5.16", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.6", "rehype-autolink-headings": "^7.1.0", "rehype-katex": "^7.0.1", "rehype-slug": "^6.0.0", "remark-frontmatter": "^5.0.0", "remark-gfm": "^4.0.1", "remark-mdx-frontmatter": "^5.2.0", "remark-toc": "^9.0.0", "tailwindcss": "^4", "typescript": "^5"}}