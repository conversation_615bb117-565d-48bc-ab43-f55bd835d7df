import type { MDXComponents } from 'mdx/types'
import Image, { type ImageProps } from 'next/image'
import clsx from 'clsx'
 
function Pre(props: React.HTMLAttributes<HTMLPreElement>) {
  return (
    <div className="group relative" data-rehype-pretty-code-fragment="">
      <pre {...props} />
    </div>
  )
}

const components = {
  // Allows customizing built-in components, e.g. to add styling.
  h1: ({ children, ...props }) => (
    <h1 className="group text-xl font-bold sm:text-3xl md:text-4xl scroll-mt-8" {...props}>{children}</h1>
  ),
  h2: ({ children, ...props }) => (
    <h2 className="group text-lg font-semibold sm:text-2xl md:text-3xl scroll-mt-6" {...props}>{children}</h2>
  ),
  h3: ({ children, ...props }) => (
    <h3 className="group text-base font-semibold sm:text-xl md:text-2xl scroll-mt-4" {...props}>{children}</h3>
  ),
  h4: ({ children, ...props }) => (
    <h4 className="group text-sm font-medium sm:text-lg md:text-xl scroll-mt-2" {...props}>{children}</h4>
  ),
  h5: ({ children, ...props }) => (
    <h5 className="group text-xs font-medium sm:text-base md:text-lg scroll-mt-2" {...props}>{children}</h5>
  ),
  h6: ({ children, ...props }) => (
    <h6 className="group text-xs font-medium sm:text-sm md:text-base scroll-mt-2" {...props}>{children}</h6>
  ),
  pre: Pre,
  img: (props) => (
    // <Image
    //   width={680}
    //   height={350}
    //   style={{ width: '100%', height: 'auto' }}
    //   {...(props as ImageProps)}
    // />
    <img className="" { ...props } />
  ),
  a: (props) => {
    const isHeadingLink = props.className?.includes('heading-link')
    const { className = '', ...restProps } = props
    return (
      <a
        className={clsx("text-foreground underline decoration-primary decoration-2 underline-offset-4 transition-colors hover:text-primary", className)}
        target={ isHeadingLink ? '' : '_blank' }
        {...restProps}
      />
    )
  }
} satisfies MDXComponents
 
export function useMDXComponents(): MDXComponents {
  return components
}
